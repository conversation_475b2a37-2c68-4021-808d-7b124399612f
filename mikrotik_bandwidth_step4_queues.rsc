# MikroTik Bandwidth Management - Step 4: Queue Tree Configuration
# Import this file after step3 to create bandwidth management queues
# Optimized for 350Mbps connection

# ============================================
# QUEUE TREE CONFIGURATION - 350Mbps Optimized
# ============================================

# Main bandwidth allocation (350Mbps connection optimized)
/queue tree add comment="Total Download Bandwidth - 350Mbps" \
    max-limit=350M name=total-download parent=ether1-LAN

/queue tree add comment="Total Upload Bandwidth - 50Mbps" \
    max-limit=50M name=total-upload parent=ether2-WAN

# Gaming traffic queues (High Priority - 30% allocation)
/queue tree add comment="Gaming Download Priority" \
    limit-at=50M max-limit=105M name=gaming-download \
    packet-mark=gaming-packet \
    parent=total-download priority=1

/queue tree add comment="Gaming Upload Priority" \
    limit-at=10M max-limit=20M name=gaming-upload \
    packet-mark=gaming-packet \
    parent=total-upload priority=1

# Teacher traffic queues (High priority - 40% allocation)
/queue tree add comment="Teacher Download" \
    limit-at=70M max-limit=140M name=teacher-download \
    parent=total-download priority=2

/queue tree add comment="Teacher Upload" \
    limit-at=15M max-limit=25M name=teacher-upload \
    parent=total-upload priority=2

# Student traffic queues (Lower priority - 50% allocation)
/queue tree add comment="Student Download" \
    limit-at=80M max-limit=175M name=student-download \
    parent=total-download priority=4

/queue tree add comment="Student Upload" \
    limit-at=15M max-limit=30M name=student-upload \
    parent=total-upload priority=4

# General traffic queue (Lowest priority - remaining bandwidth)
/queue tree add comment="General Download" \
    limit-at=20M max-limit=350M name=general-download \
    parent=total-download priority=8

/queue tree add comment="General Upload" \
    limit-at=5M max-limit=50M name=general-upload \
    parent=total-upload priority=8

:put "Step 4 Complete: Queue tree configuration created successfully"
:put "Bandwidth allocation:"
:put "- Gaming: 50M-105M download, 10M-20M upload (Priority 1)"
:put "- Teacher: 70M-140M download, 15M-25M upload (Priority 2)"
:put "- Student: 80M-175M download, 15M-30M upload (Priority 4)"
:put "- General: 20M-350M download, 5M-50M upload (Priority 8)"
:put "Next: Import mikrotik_bandwidth_step5_scheduler.rsc"
