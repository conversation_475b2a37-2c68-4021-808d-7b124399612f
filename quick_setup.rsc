# Quick Setup Script for Bandwidth Management
# Run this script after importing the main bandwidth management configuration
# This script will integrate with your existing hotspot configuration

# ============================================
# STEP 1: UPDATE EXISTING STUDENT USERS
# ============================================

# Update all existing users with 'siswa' profile to use new enhanced profile
/ip hotspot user
set [find profile=siswa] profile=siswa-basic

# ============================================
# STEP 2: CONFIGURE BANDWIDTH LIMITS
# ============================================
# Adjust these values based on your internet connection speed
# Current example assumes 50M download / 20M upload

# If you have different internet speeds, modify these values:
# For 100M/50M connection, use: max-limit=100M and max-limit=50M
# For 30M/10M connection, use: max-limit=30M and max-limit=10M

/queue tree
set [find name=total-download] max-limit=50M comment="Adjust based on your internet speed"
set [find name=total-upload] max-limit=20M comment="Adjust based on your internet speed"

# ============================================
# STEP 3: ENABLE NTP FOR TIME-BASED RULES
# ============================================
# Ensure accurate time for scheduler to work properly
/system ntp client
set enabled=yes

# ============================================
# STEP 4: CONFIGURE GAMING POLICY
# ============================================
# Choose one of the following gaming policies:

# OPTION A: PRIORITIZE GAMING (DEFAULT - ALREADY CONFIGURED)
# Gaming traffic gets priority and guaranteed bandwidth
# No additional configuration needed

# OPTION B: RESTRICT GAMING DURING SCHOOL HOURS (UNCOMMENT TO ENABLE)
# /ip firewall filter
# add action=drop chain=forward comment="Block Gaming - School Hours" \
#     connection-mark=gaming-pc-conn,gaming-mobile-conn,gaming-web-conn \
#     src-address=************/24 time=7h-15h,mon,tue,wed,thu,fri place-before=0

# OPTION C: LIMIT GAMING BANDWIDTH (UNCOMMENT TO ENABLE)
# /queue tree
# set [find name=gaming-download] max-limit=5M
# set [find name=gaming-upload] max-limit=2M

# ============================================
# STEP 5: CONFIGURE SOCIAL MEDIA BLOCKING
# ============================================
# Social media is blocked during school hours by default
# To disable social media blocking, uncomment the following line:
# /ip firewall filter disable [find comment="Block Social Media - School Hours"]

# To modify blocking hours (currently 7h-15h), use:
# /ip firewall filter set [find comment="Block Social Media - School Hours"] time=8h-16h,mon,tue,wed,thu,fri

# ============================================
# STEP 6: CUSTOMIZE TIME SCHEDULES
# ============================================
# Modify these if your school hours are different

# School starts at 07:00 (restrictive profile)
/system scheduler set [find name=school-hours-start] start-time=07:00:00

# School ends at 15:00 (extended profile)
/system scheduler set [find name=after-school-start] start-time=15:00:00

# Night time at 22:00 (gaming restricted)
/system scheduler set [find name=night-time-start] start-time=22:00:00

# ============================================
# STEP 7: ENABLE LOGGING (OPTIONAL)
# ============================================
# Enable detailed logging for monitoring
/system logging
add action=memory topics=firewall,info

# ============================================
# STEP 8: CREATE SAMPLE STUDENT ACCOUNTS
# ============================================
# Create some sample student accounts for testing
# Remove or modify these as needed

/ip hotspot user
add name=student01 password=student123 profile=siswa-basic comment="Sample Student Account"
add name=student02 password=student123 profile=siswa-basic comment="Sample Student Account"
add name=student03 password=student123 profile=siswa-basic comment="Sample Student Account"

# ============================================
# STEP 9: VERIFICATION COMMANDS
# ============================================
# Run these commands to verify the setup

:put "=== BANDWIDTH MANAGEMENT SETUP VERIFICATION ==="
:put "1. Checking Queue Tree Configuration:"
/queue tree print brief

:put "2. Checking User Profiles:"
/ip hotspot user profile print brief

:put "3. Checking Gaming Port Lists:"
/ip firewall port-list print brief where list~"gaming"

:put "4. Checking Mangle Rules:"
/ip firewall mangle print brief where comment~"Gaming"

:put "5. Checking Schedulers:"
/system scheduler print brief

:put "=== SETUP COMPLETE ==="
:put "Check the BANDWIDTH_MANAGEMENT_GUIDE.md for detailed configuration options"
:put "Monitor traffic with: /tool torch interface=ether1-LAN"
:put "Check active users with: /ip hotspot active print"

# ============================================
# OPTIONAL: PERFORMANCE MONITORING
# ============================================
# Uncomment to enable automatic performance monitoring

# /system scheduler
# add comment="Daily Bandwidth Report" interval=1d name=daily-report \
#     on-event="/log print where message~\"GAMING-TRAFFIC\" or message~\"HIGH-BANDWIDTH\"" \
#     start-date=2025-07-17 start-time=23:59:00
