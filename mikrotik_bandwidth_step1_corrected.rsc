# MikroTik Bandwidth Management - Step 1: Gaming Traffic Identification (CORRECTED)
# This script creates address lists and mangle rules to identify gaming traffic
# Compatible with 350Mbps connection

# ============================================
# GAMING SERVER ADDRESS LISTS
# ============================================

# Create address lists for popular gaming servers in Indonesia
/ip firewall address-list
add address=************/24 comment="Garena Servers" list=gaming-servers
add address=************/24 comment="Garena Servers" list=gaming-servers
add address=***********/24 comment="Steam Indonesia" list=gaming-servers
add address=************/24 comment="Steam Global" list=gaming-servers
add address=************/24 comment="Steam Global" list=gaming-servers
add address=***********/24 comment="PUBG Mobile Servers" list=gaming-servers
add address=***********/24 comment="Mobile Legends Servers" list=gaming-servers
add address=*********/24 comment="Free Fire Servers" list=gaming-servers
add address=*********/24 comment="Free Fire Servers" list=gaming-servers

# ============================================
# GAMING TRAFFIC MANGLE RULES
# ============================================

# Mark PC Gaming Traffic - PUBG
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: PUBG PC" dst-port=7080-8000 new-connection-mark=gaming-conn \
    passthrough=yes protocol=udp src-address=************/24

# Mark PC Gaming Traffic - Counter-Strike GO
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Counter-Strike GO TCP" dst-port=27015-27030,27036-27037 \
    new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Counter-Strike GO UDP" dst-port=4380,27000-27031 \
    new-connection-mark=gaming-conn passthrough=yes protocol=udp src-address=************/24

# Mark PC Gaming Traffic - DOTA 2
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: DOTA 2 TCP" dst-port=9100-9200,8230-8250,8110-8120 \
    new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: DOTA 2 UDP" dst-port=28010-28200,27010-27200,39000 \
    new-connection-mark=gaming-conn passthrough=yes protocol=udp src-address=************/24

# Mark PC Gaming Traffic - Steam
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Steam Client" dst-port=27000-27015,27014-27050 \
    new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Steam Voice" dst-port=3478,4379,4380 \
    new-connection-mark=gaming-conn passthrough=yes protocol=udp src-address=************/24

# Mark PC Gaming Traffic - Fortnite
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Fortnite" dst-port=9000-9100 \
    new-connection-mark=gaming-conn passthrough=yes protocol=udp src-address=************/24

# Mark PC Gaming Traffic - Point Blank
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Point Blank TCP" dst-port=39190-39200 \
    new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Point Blank UDP" dst-port=40000-40010 \
    new-connection-mark=gaming-conn passthrough=yes protocol=udp src-address=************/24

# Mark Mobile Gaming Traffic - Free Fire
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Free Fire TCP" dst-port=39003,39698,39779 \
    new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Free Fire UDP" dst-port=10001,10003,10012 \
    new-connection-mark=gaming-conn passthrough=yes protocol=udp src-address=************/24

# Mark Mobile Gaming Traffic - PUBG Mobile
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: PUBG Mobile TCP" dst-port=10012,17500 \
    new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: PUBG Mobile UDP" dst-port=10010,10013,10039,10096,10491,10612,11455,12235,13748,13894,13972,20000-20002 \
    new-connection-mark=gaming-conn passthrough=yes protocol=udp src-address=************/24

# Mark Mobile Gaming Traffic - Mobile Legends
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Mobile Legends" dst-port=5001,5003,9001,30000-30200 \
    new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

# Mark Mobile Gaming Traffic - Clash of Clans
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Clash of Clans" dst-port=9330-9340 \
    new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

# Mark Mobile Gaming Traffic - Arena of Valor
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Arena of Valor TCP" dst-port=10001-10094 \
    new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Arena of Valor UDP" dst-port=10080,17000 \
    new-connection-mark=gaming-conn passthrough=yes protocol=udp src-address=************/24

# Mark Web Gaming Traffic - Roblox
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Roblox" dst-port=49152-65535 \
    new-connection-mark=gaming-conn passthrough=yes protocol=udp src-address=************/24

# Mark gaming traffic by server address
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Server Address" dst-address-list=gaming-servers \
    new-connection-mark=gaming-conn passthrough=yes src-address=************/24

# Mark gaming packets
/ip firewall mangle add action=mark-packet chain=prerouting \
    comment="Gaming: Mark Packets" connection-mark=gaming-conn \
    new-packet-mark=gaming-packet passthrough=no

:put "Step 1 Complete: Gaming traffic identification rules created"
:put "Gaming traffic will be marked with connection-mark=gaming-conn and packet-mark=gaming-packet"
:put "Next: Import mikrotik_bandwidth_step2_profiles.rsc"
