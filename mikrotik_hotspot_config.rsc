# 2025-07-17 11:12:30 by RouterOS 7.19.3
# software id = 53U8-H2FW
#
/interface ethernet
set [ find default-name=ether1 ] disable-running-check=no name=ether1-LAN
set [ find default-name=ether2 ] disable-running-check=no name=ether2-WAN
/interface wireless security-profiles
set [ find default=yes ] supplicant-identity=MikroTik
/ip hotspot profile
set [ find default=yes ] html-directory=hotspot
add dns-name=wifi.yapip.sch.id hotspot-address=************ html-directory=\
    flash/hotspot name=hsprof1
/ip hotspot user profile
add name=guru shared-users=unlimited
add name=siswa rate-limit=2M/2M shared-users=unlimited
/ip pool
add name=dhcp_pool0 ranges=************-************54
/ip dhcp-server
add address-pool=dhcp_pool0 interface=ether1-LAN name=dhcp1
/ip hotspot
add address-pool=dhcp_pool0 disabled=no interface=ether1-LAN name=hotspot1 \
    profile=hsprof1
/port
set 0 name=serial0
set 1 name=serial4
/ip address
add address=************/24 interface=ether1-LAN network=************
/ip dhcp-client
add default-route-tables=main interface=ether2-WAN
/ip dhcp-server network
add address=************/24 dns-server=************ gateway=************
/ip dns
set allow-remote-requests=yes servers=*******,*******
/ip firewall filter
add action=drop chain=input connection-state=invalid
add action=accept chain=input connection-state=established,related
add action=accept chain=forward connection-state=established,related
add action=passthrough chain=forward connection-state=new
add action=passthrough chain=unused-hs-chain comment=\
    "place hotspot rules here" disabled=yes
/ip firewall nat
add action=passthrough chain=unused-hs-chain comment=\
    "place hotspot rules here" disabled=yes
add action=masquerade chain=srcnat comment="masquerade hotspot network" \
    src-address=************/24
/ip hotspot ip-binding
add comment="Server LAN" mac-address=BC:AE:C5:92:AB:5D type=bypassed
add comment="Web Server LAN" mac-address=3A:5B:7F:C1:4D:F5 type=bypassed
add comment="Bu Citra (SMP)" mac-address=9C:5F:5A:3C:3A:89 type=bypassed
add comment="Bu Fatima" mac-address=E0:62:67:3D:14:DB type=bypassed
add comment="Bu Harfiani Kepsek SMK (Tab)" mac-address=6E:08:2C:EA:8A:3D \
    type=bypassed
add comment="Bu Harfiani Kepsek SMK" mac-address=E4:6A:35:A3:CB:73 type=\
    bypassed
add comment="Bu Harfiani Kepsek SMK" mac-address=88:F8:72:08:76:3F type=\
    bypassed
add comment="Bu Harfiani Kepsek SMK" mac-address=C4:E3:9F:D0:47:83 type=\
    bypassed
add comment="Bu Harfiani Kepsek SMK" mac-address=20:5E:F7:DC:7B:48 type=\
    bypassed
add comment="Bu Harfiani Kepsek SMK (iPhone)" mac-address=BC:09:63:62:3E:9C \
    type=bypassed
add comment="Bu Hj Paridah (HP)" mac-address=40:8E:F6:CD:AC:63 type=bypassed
add comment="Bu Ian" mac-address=88:5A:06:EC:EF:E1 type=bypassed
add comment="Bu Ian" mac-address=0C:A8:A7:C4:AD:86 type=bypassed
add comment="Bu Ian" mac-address=30:CB:F8:F1:2D:8F type=bypassed
add comment="Bu Indah (SMP)" mac-address=28:02:2E:4A:DB:51 type=bypassed
add comment="Bu Irma (Laptop)" mac-address=48:C4:61:3A:A8:71 type=bypassed
add comment="Bu Irma" mac-address=AC:74:B1:7F:94:96 type=bypassed
add comment="Bu Irma HP" mac-address=44:FE:EF:43:E1:3F type=bypassed
add comment="Bu Irma (Laptop)" mac-address=2E:33:12:D5:87:8B type=bypassed
add comment="Bu Irma" mac-address=30:AF:CE:05:F9:BF type=bypassed
add comment="Bu Irma" mac-address=34:E9:11:06:3F:AB type=bypassed
add comment="Bu Irma (Tab)" mac-address=C0:87:EB:E8:8E:33 type=bypassed
add comment="Bu Irma Kepsek SMA" mac-address=BC:B2:CC:1C:7F:92 type=bypassed
add comment="Bu Irma SMA" mac-address=E8:5F:B4:3B:1E:D1 type=bypassed
add comment="Bu Lina" mac-address=00:C7:11:4B:37:D6 type=bypassed
add comment="Bu Mila" mac-address=88:5A:06:64:B2:B9 type=bypassed
add comment="Bu Ningsih (HP)" mac-address=C0:ED:E5:4A:C9:C3 type=bypassed
add comment="Bu Nurmi" mac-address=6E:6B:82:6C:79:55 type=bypassed
add comment="Bu Reski" mac-address=B4:9D:02:D2:29:1B type=bypassed
add comment="Bu Sri" mac-address=A4:D9:90:FC:F1:D9 type=bypassed
add comment=Ikhsan mac-address=70:F1:A1:BC:42:2D type=bypassed
add comment="Ikhsan Note)" mac-address=E8:50:8B:16:A7:51 type=bypassed
add comment="Ikhsan iPhone" disabled=yes mac-address=6C:72:E7:D2:14:04 type=\
    bypassed
add comment="Ikhsan Tab" mac-address=B0:9C:63:5A:ED:5C type=bypassed
add comment="Jumahir (HP)" mac-address=8C:45:00:04:7D:A6 type=bypassed
add comment="P. Hasbi" mac-address=E4:E2:6C:0F:18:E3 type=bypassed
add comment="P. Adit" mac-address=90:DF:7D:82:EE:69 type=bypassed
add comment="P. Anto (Tab)" mac-address=B4:31:61:0E:1A:A7 type=bypassed
add comment="P. Anto (TV)" mac-address=C8:90:8A:93:31:59 type=bypassed
add comment="P. Anto (Laptop)" mac-address=74:40:BE:02:58:24 type=bypassed
add comment="P. Anto (Anak)" mac-address=00:F4:8D:7C:9D:53 type=bypassed
add comment="P. Anto Anak (Laptop)" mac-address=3C:A2:C3:83:51:E8 type=\
    bypassed
add comment="P. Hasbi" mac-address=14:DD:9C:F7:1F:71 type=bypassed
add comment="P. Hasbi" mac-address=30:4F:00:75:93:3D type=bypassed
add comment="P. Kasim" mac-address=74:73:B4:3A:13:CE type=bypassed
add comment="P. Kasim (Laptop)" mac-address=88:A9:B7:63:99:8E type=bypassed
add comment="P. Nas" mac-address=30:52:CB:7D:B4:8B type=bypassed
add comment="P. Takwa (Laptop)" mac-address=DC:F5:05:84:D8:1F type=bypassed
add comment="P. Takwa (Laptop)" mac-address=AC:B5:7D:00:38:51 type=bypassed
add comment="P. Taqwa (HP)" mac-address=F0:79:59:80:28:9F type=bypassed
add comment="P. Taqwa" mac-address=08:7F:98:DA:9D:5D type=bypassed
add comment="Bu Citra (SMP)" mac-address=48:29:D6:11:52:D3 type=bypassed
add comment="Laptop - LAB (WLAN)" mac-address=2C:D0:5A:44:00:CB type=bypassed
add comment="Bu Nurmi" mac-address=FC:70:2E:78:8A:AD type=bypassed
add comment="Bu Guru SMP" mac-address=4C:B0:4A:CA:28:D0 type=bypassed
/ip hotspot user
add name=admin
/system clock
set time-zone-name=Asia/Makassar
/system ntp client
set enabled=yes
/system ntp client servers
add address=id.pool.ntp.org
