# 🚀 MikroTik Bandwidth Management - Installation Guide (CORRECTED)

## ⚠️ PERBAIKAN SYNTAX ERROR
Script telah diperbaiki untuk mengatasi masalah:
- ❌ **Removed**: `/ip firewall port-list` (tidak ada di MikroTik)
- ✅ **Fixed**: Menggunakan mangle rules langsung dengan port ranges
- ✅ **Simplified**: Gaming traffic menggunakan satu connection-mark dan packet-mark

## 📋 Pre-Installation Checklist

1. **Backup Configuration**:
   ```bash
   /export file=backup-before-bandwidth-management
   ```

2. **Check Current System**:
   ```bash
   /system resource print
   /interface print
   /ip hotspot print
   ```

## 🔧 Step-by-Step Installation (CORRECTED)

### Step 1: Gaming Traffic Identification (CORRECTED)
```bash
/import mikrotik_bandwidth_step1_corrected.rsc
```
**What it does**: 
- Creates gaming server address lists
- Creates mangle rules to identify gaming traffic by ports
- Marks gaming traffic with `gaming-conn` and `gaming-packet`

### Step 2: User Profiles  
```bash
/import mikrotik_bandwidth_step2_profiles.rsc
```
**What it does**: Creates 4 student profiles optimized for 350Mbps

### Step 3: Traffic Marking (SKIP - Already done in Step 1)
```bash
/import mikrotik_bandwidth_step3_mangle.rsc
```
**What it does**: This step is now skipped as mangle rules are in Step 1

### Step 4: Queue Management
```bash
/import mikrotik_bandwidth_step4_queues.rsc
```
**What it does**: Creates bandwidth allocation queues using `gaming-packet` mark

### Step 5: Time Scheduler
```bash
/import mikrotik_bandwidth_step5_scheduler.rsc
```
**What it does**: Creates automatic profile switching based on time

### Step 6: Final Configuration
```bash
/import mikrotik_bandwidth_step6_final.rsc
```
**What it does**: Completes setup with monitoring and user updates

## ✅ Verification Commands

```bash
# Check gaming server address lists
/ip firewall address-list print where list=gaming-servers

# Check gaming mangle rules
/ip firewall mangle print where comment~"Gaming"

# Check user profiles
/ip hotspot user profile print brief

# Check queue tree
/queue tree print brief

# Check schedulers
/system scheduler print brief
```

## 🎮 Gaming Traffic Detection

### Supported Games with Port Detection:
- **PUBG PC**: UDP 7080-8000
- **Counter-Strike GO**: TCP 27015-27030, UDP 4380,27000-27031
- **DOTA 2**: TCP 9100-9200, UDP 28010-28200
- **Steam**: TCP 27000-27015, UDP 3478,4379,4380
- **Fortnite**: UDP 9000-9100
- **Point Blank**: TCP 39190-39200, UDP 40000-40010
- **Free Fire**: TCP 39003,39698,39779, UDP 10001,10003,10012
- **PUBG Mobile**: TCP 10012,17500, UDP 10010-20002
- **Mobile Legends**: TCP 5001,5003,9001,30000-30200
- **Clash of Clans**: TCP 9330-9340
- **Arena of Valor**: TCP 10001-10094, UDP 10080,17000
- **Roblox**: UDP 49152-65535

### Gaming Server Detection:
- **Garena Servers**: ************/24, ************/24
- **Steam Servers**: ***********/24, ************/24
- **PUBG Mobile**: ***********/24
- **Mobile Legends**: ***********/24
- **Free Fire**: 47.74.1.0/24, 47.74.2.0/24

## 📊 Expected Results After Installation

### Bandwidth Allocation (350Mbps Total):
- **Gaming Traffic**: 50M-105M download, 10M-20M upload (Priority 1)
- **Teacher Traffic**: 70M-140M download, 15M-25M upload (Priority 2)  
- **Student Traffic**: 80M-175M download, 15M-30M upload (Priority 4)
- **General Traffic**: 20M-350M download, 5M-50M upload (Priority 8)

### Student Profile Speeds:
| Time | Profile | Upload | Download | Improvement |
|------|---------|--------|----------|-------------|
| **07:00-15:00** | siswa-basic | 5M | 10M | **5x faster** |
| **15:00-22:00** | siswa-extended | 10M | 20M | **10x faster** |
| **22:00-07:00** | siswa-no-gaming | 3M | 5M | **2.5x faster** |
| **Weekend** | siswa-premium | 15M | 30M | **15x faster** |

## 🔍 Monitoring Gaming Traffic

### Check Gaming Traffic Detection:
```bash
# Monitor gaming connections
/ip firewall connection print where connection-mark=gaming-conn

# Check gaming packet marking
/tool torch interface=ether1-LAN src-address=************/24

# Gaming traffic statistics
/queue tree print stats where name=gaming-download
/queue tree print stats where name=gaming-upload
```

### Real-time Gaming Monitoring:
```bash
# Enable gaming traffic logging
/ip firewall filter enable [find comment="Log Gaming Traffic"]

# View gaming logs
/log print where message~"GAMING-TRAFFIC"

# Monitor specific gaming ports
/tool torch interface=ether1-LAN port=5001,9001,10012,39003
```

## 🛠️ Troubleshooting

### Gaming Traffic Not Detected:
1. **Check mangle rules**:
   ```bash
   /ip firewall mangle print where comment~"Gaming"
   ```

2. **Verify connection marking**:
   ```bash
   /ip firewall connection print where connection-mark=gaming-conn
   ```

3. **Test specific game ports**:
   ```bash
   /tool torch interface=ether1-LAN port=5001
   ```

### Queue Not Working:
1. **Check packet marking**:
   ```bash
   /queue tree print stats where packet-mark=gaming-packet
   ```

2. **Verify queue tree structure**:
   ```bash
   /queue tree print brief
   ```

## 🎯 Gaming Performance Testing

### Test Gaming Latency:
```bash
# Test to gaming servers
/tool ping ************ count=10
/tool ping *********** count=10
```

### Monitor Gaming Bandwidth:
```bash
# Real-time gaming traffic
/tool torch interface=ether1-LAN connection-mark=gaming-conn

# Gaming queue statistics
/queue tree print stats where name~"gaming"
```

## 🔧 Advanced Configuration

### Add More Gaming Servers:
```bash
/ip firewall address-list add address=NEW.SERVER.IP.0/24 comment="New Game Server" list=gaming-servers
```

### Add Custom Gaming Ports:
```bash
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Custom Game" dst-port=CUSTOM-PORT \
    new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24
```

### Adjust Gaming Priority:
```bash
# Increase gaming bandwidth
/queue tree set [find name=gaming-download] limit-at=75M max-limit=150M
/queue tree set [find name=gaming-upload] limit-at=15M max-limit=30M

# Decrease gaming bandwidth
/queue tree set [find name=gaming-download] limit-at=25M max-limit=50M
/queue tree set [find name=gaming-upload] limit-at=5M max-limit=10M
```

## 📞 Support

Script yang sudah diperbaiki ini menggunakan:
- ✅ **Mangle rules** dengan port ranges langsung
- ✅ **Address lists** untuk gaming servers
- ✅ **Single connection mark** (`gaming-conn`) untuk semua gaming traffic
- ✅ **Single packet mark** (`gaming-packet`) untuk queue management

Instalasi sekarang akan berjalan tanpa error syntax!
