# MikroTik Simple Gaming Ports - Error-Free Version
# This script creates basic gaming port lists without complex syntax

# Create gaming port lists - PC Games
/ip firewall port-list
add comment="Steam" list=gaming-pc ports=27015
add comment="Steam 2" list=gaming-pc ports=27016
add comment="Steam 3" list=gaming-pc ports=27017
add comment="DOTA 2" list=gaming-pc ports=27015-27030
add comment="Counter Strike" list=gaming-pc ports=27015-27030
add comment="PUBG PC" list=gaming-pc ports=7777
add comment="Fortnite" list=gaming-pc ports=9000-9100
add comment="Point Blank" list=gaming-pc ports=39190-39200

# Mobile Games
add comment="Free Fire" list=gaming-mobile ports=39003
add comment="PUBG Mobile" list=gaming-mobile ports=10012
add comment="Mobile Legends" list=gaming-mobile ports=5001
add comment="Clash of Clans" list=gaming-mobile ports=9330-9340
add comment="Arena of Valor" list=gaming-mobile ports=10080

# Web Games  
add comment="Roblox" list=gaming-web ports=53
add comment="8 Ball Pool" list=gaming-web ports=4000

:put "Simple gaming ports created successfully"
:put "Next: Import step 2 profiles"
