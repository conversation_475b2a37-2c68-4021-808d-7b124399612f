# MikroTik Bandwidth Management - Alternative Approach
# This version uses direct mangle rules instead of complex port lists
# Optimized for 350Mbps connection

# ============================================
# STEP 1: CREATE USER PROFILES FIRST
# ============================================

# Remove existing siswa profile if exists
:if ([/ip hotspot user profile find name=siswa] != "") do={
    /ip hotspot user profile remove [find name=siswa]
}

# Student Profile - Basic (School Hours: 07:00-15:00)
/ip hotspot user profile add name=siswa-basic rate-limit=5M/10M shared-users=1 session-timeout=4h

# Student Profile - Extended (After School: 15:00-22:00)
/ip hotspot user profile add name=siswa-extended rate-limit=10M/20M shared-users=1 session-timeout=6h

# Student Profile - Gaming Restricted (Night time)
/ip hotspot user profile add name=siswa-no-gaming rate-limit=3M/5M shared-users=1 session-timeout=2h

# Student Profile - Premium (Weekend)
/ip hotspot user profile add name=siswa-premium rate-limit=15M/30M shared-users=1 session-timeout=8h

# ============================================
# STEP 2: CREATE GAMING TRAFFIC MANGLE RULES
# ============================================

# Mark gaming traffic directly without port-list
# Steam and DOTA 2
/ip firewall mangle add action=mark-connection chain=prerouting comment="Gaming Steam" dst-port=27015-27030 new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

# PUBG PC
/ip firewall mangle add action=mark-connection chain=prerouting comment="Gaming PUBG PC" dst-port=7777,7080-8000 new-connection-mark=gaming-conn passthrough=yes protocol=udp src-address=************/24

# Mobile Legends
/ip firewall mangle add action=mark-connection chain=prerouting comment="Gaming Mobile Legends" dst-port=5001,9001,30000-30200 new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

# Free Fire
/ip firewall mangle add action=mark-connection chain=prerouting comment="Gaming Free Fire" dst-port=39003,10001,10012 new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

# PUBG Mobile
/ip firewall mangle add action=mark-connection chain=prerouting comment="Gaming PUBG Mobile" dst-port=10012,17500,20000-20002 new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

# Clash of Clans
/ip firewall mangle add action=mark-connection chain=prerouting comment="Gaming COC" dst-port=9330-9340 new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24

# Mark gaming packets
/ip firewall mangle add action=mark-packet chain=prerouting comment="Gaming Packets" connection-mark=gaming-conn new-packet-mark=gaming-packet passthrough=no

# ============================================
# STEP 3: CREATE QUEUE TREE - 350Mbps Optimized
# ============================================

# Main bandwidth allocation
/queue tree add comment="Total Download - 350Mbps" max-limit=350M name=total-download parent=ether1-LAN
/queue tree add comment="Total Upload - 50Mbps" max-limit=50M name=total-upload parent=ether2-WAN

# Gaming traffic queues (Priority 1)
/queue tree add comment="Gaming Download" limit-at=50M max-limit=105M name=gaming-download packet-mark=gaming-packet parent=total-download priority=1
/queue tree add comment="Gaming Upload" limit-at=10M max-limit=20M name=gaming-upload packet-mark=gaming-packet parent=total-upload priority=1

# Student traffic queues (Priority 4)
/queue tree add comment="Student Download" limit-at=80M max-limit=175M name=student-download parent=total-download priority=4
/queue tree add comment="Student Upload" limit-at=15M max-limit=30M name=student-upload parent=total-upload priority=4

# Teacher traffic queues (Priority 2)
/queue tree add comment="Teacher Download" limit-at=70M max-limit=140M name=teacher-download parent=total-download priority=2
/queue tree add comment="Teacher Upload" limit-at=15M max-limit=25M name=teacher-upload parent=total-upload priority=2

# ============================================
# STEP 4: TIME-BASED SCHEDULER
# ============================================

# School hours (07:00-15:00)
/system scheduler add comment="School Hours" interval=1d name=school-start on-event=":foreach user in=[/ip hotspot user find profile~\"siswa-\"] do={/ip hotspot user set \$user profile=siswa-basic}" start-date=2025-07-17 start-time=07:00:00

# After school (15:00-22:00)
/system scheduler add comment="After School" interval=1d name=after-school on-event=":foreach user in=[/ip hotspot user find profile~\"siswa-\"] do={/ip hotspot user set \$user profile=siswa-extended}" start-date=2025-07-17 start-time=15:00:00

# Night time (22:00-07:00)
/system scheduler add comment="Night Time" interval=1d name=night-time on-event=":foreach user in=[/ip hotspot user find profile~\"siswa-\"] do={/ip hotspot user set \$user profile=siswa-no-gaming}" start-date=2025-07-17 start-time=22:00:00

# ============================================
# STEP 5: UPDATE EXISTING USERS
# ============================================

# Update existing student users
:foreach user in=[/ip hotspot user find profile=siswa] do={
    /ip hotspot user set $user profile=siswa-basic
}

# Enable NTP
/system ntp client set enabled=yes

:put "============================================"
:put "BANDWIDTH MANAGEMENT SETUP COMPLETE"
:put "============================================"
:put "Configuration for 350Mbps connection:"
:put "- Gaming: 50M-105M download, 10M-20M upload (Priority 1)"
:put "- Student Basic: 5M/10M (School hours)"
:put "- Student Extended: 10M/20M (After school)"
:put "- Student Premium: 15M/30M (Weekend)"
:put "- Teacher: 70M-140M download, 15M-25M upload"
:put ""
:put "Monitor with: /queue tree print stats"
:put "Check users: /ip hotspot active print"
