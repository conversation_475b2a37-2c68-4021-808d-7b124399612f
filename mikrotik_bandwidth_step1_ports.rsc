# MikroTik Bandwidth Management - Step 1: Gaming Ports Configuration
# Import this file first to create gaming port lists
# Compatible with 350Mbps connection

# ============================================
# GAMING PORT LISTS CONFIGURATION
# ============================================

# Create address lists for gaming traffic identification
/ip firewall address-list
add address=0.0.0.0/0 comment="Gaming Traffic - All destinations" list=gaming-destinations

# Create port groups for PC games - Simplified version
/ip firewall port-list
add comment="APEX Legends" list=gaming-pc ports=9960-9969
add comment="APEX Legends 2" list=gaming-pc ports=1024-1124
add comment="APEX Legends 3" list=gaming-pc ports=3216
add comment="APEX Legends 4" list=gaming-pc ports=18000
add comment="APEX Legends 5" list=gaming-pc ports=27900
add comment="PUBG PC" list=gaming-pc ports=7080-8000
add comment="Counter-Strike GO" list=gaming-pc ports=27015-27030
add comment="Counter-Strike GO 2" list=gaming-pc ports=27036-27037
add comment="Counter-Strike GO 3" list=gaming-pc ports=4380
add comment="DOTA 2" list=gaming-pc ports=9100-9200
add comment="DOTA 2 UDP" list=gaming-pc ports=28010-28200
add comment="DOTA 2 UDP2" list=gaming-pc ports=27010-27200
add comment="Fortnite" list=gaming-pc ports=9000-9100
add comment="Steam Client" list=gaming-pc ports=27000-27015
add comment="Steam Downloads" list=gaming-pc ports=27014-27050
add comment="Steam Voice" list=gaming-pc ports=3478
add comment="Steam Voice 2" list=gaming-pc ports=4379
add comment="Point Blank" list=gaming-pc ports=39190-39200
add comment="Point Blank UDP" list=gaming-pc ports=40000-40010
add comment="Cross Fire" list=gaming-pc ports=10009
add comment="Cross Fire 2" list=gaming-pc ports=13008
add comment="Cross Fire UDP" list=gaming-pc ports=12020-12080
add comment="Dragon Nest" list=gaming-pc ports=14300-15512

# Create port groups for mobile games - Simplified
add comment="Free Fire Mobile" list=gaming-mobile ports=39003
add comment="Free Fire Mobile 2" list=gaming-mobile ports=39698
add comment="Free Fire Mobile 3" list=gaming-mobile ports=10001
add comment="PUBG Mobile" list=gaming-mobile ports=10012
add comment="PUBG Mobile 2" list=gaming-mobile ports=17500
add comment="PUBG Mobile 3" list=gaming-mobile ports=10010
add comment="PUBG Mobile 4" list=gaming-mobile ports=20000-20002
add comment="Mobile Legends" list=gaming-mobile ports=5001
add comment="Mobile Legends 2" list=gaming-mobile ports=9001
add comment="Mobile Legends 3" list=gaming-mobile ports=30000-30200
add comment="Clash of Clans" list=gaming-mobile ports=9330-9340
add comment="Arena of Valor" list=gaming-mobile ports=10001-10094
add comment="Arena of Valor 2" list=gaming-mobile ports=10080
add comment="Rules of Survival" list=gaming-mobile ports=24000-24050

# Create port groups for web games
add comment="Roblox" list=gaming-web ports=49152-65535
add comment="8 Ball Pool" list=gaming-web ports=4000
add comment="League of Angels 2" list=gaming-web ports=51700-51715

:put "Step 1 Complete: Gaming port lists created successfully"
:put "Next: Import mikrotik_bandwidth_step2_profiles.rsc"
