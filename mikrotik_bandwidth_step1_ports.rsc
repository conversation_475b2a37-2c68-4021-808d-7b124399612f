# MikroTik Bandwidth Management - Step 1: Gaming Ports Configuration
# Import this file first to create gaming port lists
# Compatible with 350Mbps connection

# ============================================
# GAMING PORT LISTS CONFIGURATION
# ============================================

# Create address lists for gaming traffic identification
/ip firewall address-list
add address=0.0.0.0/0 comment="Gaming Traffic - All destinations" list=gaming-destinations

# Create port groups for PC games
/ip firewall port-list
add comment="APEX Legends TCP" list=gaming-pc ports=9960-9969
add comment="APEX Legends TCP2" list=gaming-pc ports=1024-1124
add comment="APEX Legends TCP3" list=gaming-pc ports=3216,18000,18120,18060
add comment="APEX Legends TCP4" list=gaming-pc ports=27900,28910,29900
add comment="PUBG PC" list=gaming-pc ports=7080-8000
add comment="Counter-Strike GO TCP" list=gaming-pc ports=27015-27030
add comment="Counter-Strike GO TCP2" list=gaming-pc ports=27036-27037
add comment="Counter-Strike GO UDP" list=gaming-pc ports=4380,27000-27031
add comment="DOTA 2 TCP" list=gaming-pc ports=9100-9200
add comment="DOTA 2 TCP2" list=gaming-pc ports=8230-8250,8110-8120
add comment="DOTA 2 UDP" list=gaming-pc ports=28010-28200
add comment="DOTA 2 UDP2" list=gaming-pc ports=27010-27200,39000
add comment="Fortnite" list=gaming-pc ports=9000-9100
add comment="Steam Client" list=gaming-pc ports=27000-27015
add comment="Steam Downloads" list=gaming-pc ports=27014-27050
add comment="Steam Voice" list=gaming-pc ports=3478,4379,4380
add comment="Point Blank TCP" list=gaming-pc ports=39190-39200
add comment="Point Blank UDP" list=gaming-pc ports=40000-40010
add comment="Cross Fire TCP" list=gaming-pc ports=10009,13008,16666,28012
add comment="Cross Fire UDP" list=gaming-pc ports=12020-12080
add comment="Cross Fire UDP2" list=gaming-pc ports=13000-13080
add comment="Dragon Nest TCP" list=gaming-pc ports=14300-15512
add comment="Dragon Nest UDP" list=gaming-pc ports=15000-15500

# Create port groups for mobile games
add comment="Free Fire Mobile TCP" list=gaming-mobile ports=39003,39698,39779
add comment="Free Fire Mobile UDP" list=gaming-mobile ports=10001,10003,10012
add comment="PUBG Mobile TCP" list=gaming-mobile ports=10012,17500
add comment="PUBG Mobile UDP1" list=gaming-mobile ports=10010,10013,10039
add comment="PUBG Mobile UDP2" list=gaming-mobile ports=10096,10491,10612
add comment="PUBG Mobile UDP3" list=gaming-mobile ports=11455,12235,13748
add comment="PUBG Mobile UDP4" list=gaming-mobile ports=13894,13972
add comment="PUBG Mobile UDP5" list=gaming-mobile ports=20000-20002
add comment="Mobile Legends TCP" list=gaming-mobile ports=5001,5003,9001
add comment="Mobile Legends Range" list=gaming-mobile ports=30000-30200
add comment="Clash of Clans" list=gaming-mobile ports=9330-9340
add comment="Arena of Valor TCP" list=gaming-mobile ports=10001-10094
add comment="Arena of Valor UDP" list=gaming-mobile ports=10080,17000
add comment="Rules of Survival Mobile" list=gaming-mobile ports=24000-24050

# Create port groups for web games
add comment="Roblox" list=gaming-web ports=49152-65535
add comment="8 Ball Pool" list=gaming-web ports=4000
add comment="League of Angels 2" list=gaming-web ports=51700-51715

:put "Step 1 Complete: Gaming port lists created successfully"
:put "Next: Import mikrotik_bandwidth_step2_profiles.rsc"
