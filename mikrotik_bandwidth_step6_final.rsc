# MikroTik Bandwidth Management - Step 6: Final Configuration
# Import this file last to complete the setup with content filtering and monitoring

# ============================================
# CONTENT FILTERING FOR STUDENTS
# ============================================

# Create social media address list
/ip firewall address-list add address=facebook.com comment="Social Media Block" list=social-media
/ip firewall address-list add address=instagram.com comment="Social Media Block" list=social-media
/ip firewall address-list add address=twitter.com comment="Social Media Block" list=social-media
/ip firewall address-list add address=tiktok.com comment="Social Media Block" list=social-media
/ip firewall address-list add address=youtube.com comment="Social Media Block" list=social-media

# Block social media during school hours (optional - disabled by default)
/ip firewall filter add action=reject chain=forward \
    comment="Block Social Media - School Hours" \
    dst-address-list=social-media reject-with=icmp-network-unreachable \
    src-address=************/24 time=7h-15h,mon,tue,wed,thu,fri disabled=yes

# ============================================
# MONITORING AND LOGGING
# ============================================

# Log gaming traffic (optional - disabled by default)
/ip firewall filter add action=passthrough chain=forward \
    comment="Log Gaming Traffic" \
    connection-mark=gaming-pc-conn,gaming-mobile-conn,gaming-web-conn \
    log=yes log-prefix="GAMING-TRAFFIC" disabled=yes

# Log high bandwidth usage (optional - disabled by default)
/ip firewall filter add action=passthrough chain=forward \
    comment="Log High Bandwidth Usage" \
    connection-bytes=100000000-4294967295 log=yes \
    log-prefix="HIGH-BANDWIDTH" disabled=yes

# ============================================
# UPDATE EXISTING USERS TO NEW PROFILE
# ============================================

# Update existing student users to use new basic profile
:foreach user in=[/ip hotspot user find profile=siswa] do={
    /ip hotspot user set $user profile=siswa-basic
    :put ("Updated user: " . [/ip hotspot user get $user name] . " to siswa-basic profile")
}

# ============================================
# ENABLE NTP FOR ACCURATE TIME
# ============================================

# Ensure NTP is enabled for scheduler to work properly
/system ntp client set enabled=yes

# ============================================
# FINAL VERIFICATION AND SUMMARY
# ============================================

:put "============================================"
:put "MIKROTIK BANDWIDTH MANAGEMENT SETUP COMPLETE"
:put "============================================"
:put ""
:put "Configuration Summary:"
:put "- Total Bandwidth: 350M download / 50M upload"
:put "- Gaming Priority: Enabled with guaranteed bandwidth"
:put "- Student Profiles: 4 time-based profiles created"
:put "- Teacher Priority: High priority bandwidth allocation"
:put "- Time-based Scheduling: Automatic profile switching"
:put "- Content Filtering: Social media blocking (disabled by default)"
:put ""
:put "Student Profile Speeds:"
:put "- siswa-basic (School): 5M/10M"
:put "- siswa-extended (After school): 10M/20M"
:put "- siswa-no-gaming (Night): 3M/5M"
:put "- siswa-premium (Weekend): 15M/30M"
:put ""
:put "Gaming Traffic:"
:put "- Download: 50M guaranteed, up to 105M maximum"
:put "- Upload: 10M guaranteed, up to 20M maximum"
:put "- Priority: Highest (Priority 1)"
:put ""
:put "Monitoring Commands:"
:put "- Check queues: /queue tree print stats"
:put "- Active users: /ip hotspot active print"
:put "- Traffic monitor: /tool torch interface=ether1-LAN"
:put ""
:put "To enable social media blocking during school hours:"
:put "/ip firewall filter enable [find comment=\"Block Social Media - School Hours\"]"
:put ""
:put "To enable gaming traffic logging:"
:put "/ip firewall filter enable [find comment=\"Log Gaming Traffic\"]"
:put ""
:put "Setup completed successfully!"
:put "Check BANDWIDTH_MANAGEMENT_GUIDE.md for detailed configuration options"
