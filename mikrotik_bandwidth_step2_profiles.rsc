# MikroTik Bandwidth Management - Step 2: User Profiles Configuration
# Import this file after step1 to create enhanced user profiles
# Optimized for 350Mbps connection

# ============================================
# ENHANCED USER PROFILES - 350Mbps Optimized
# ============================================

# Remove existing siswa profile if exists
:if ([/ip hotspot user profile find name=siswa] != "") do={
    /ip hotspot user profile remove [find name=siswa]
}

# Student Profile - Single profile optimized for 350Mbps
/ip hotspot user profile add name=siswa \
    rate-limit=10M/20M \
    shared-users=1 \
    session-timeout=8h \
    idle-timeout=60m \
    keepalive-timeout=2m \
    status-autorefresh=1m \
    transparent-proxy=yes

:put "Step 2 Complete: User profile created successfully"
:put "Profile created: siswa (10M upload / 20M download)"
:put "This replaces the original 2M/2M siswa profile with 10x faster speeds"
:put "Next: Import mikrotik_bandwidth_step3_mangle.rsc"
