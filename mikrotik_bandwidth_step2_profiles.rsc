# MikroTik Bandwidth Management - Step 2: User Profiles Configuration
# Import this file after step1 to create enhanced user profiles
# Optimized for 350Mbps connection

# ============================================
# ENHANCED USER PROFILES - 350Mbps Optimized
# ============================================

# Remove existing siswa profile if exists
:if ([/ip hotspot user profile find name=siswa] != "") do={
    /ip hotspot user profile remove [find name=siswa]
}

# Student Profile - Basic (School Hours: 07:00-15:00) - Optimized for 350Mbps
/ip hotspot user profile add name=siswa-basic \
    rate-limit=5M/10M \
    shared-users=1 \
    session-timeout=4h \
    idle-timeout=30m \
    keepalive-timeout=2m \
    status-autorefresh=1m \
    transparent-proxy=yes

# Student Profile - Extended (After School: 15:00-22:00) - Higher speeds
/ip hotspot user profile add name=siswa-extended \
    rate-limit=10M/20M \
    shared-users=1 \
    session-timeout=6h \
    idle-timeout=45m \
    keepalive-timeout=2m \
    status-autorefresh=1m \
    transparent-proxy=yes

# Student Profile - Gaming Restricted (Night time)
/ip hotspot user profile add name=siswa-no-gaming \
    rate-limit=3M/5M \
    shared-users=1 \
    session-timeout=2h \
    idle-timeout=15m \
    keepalive-timeout=2m \
    status-autorefresh=1m \
    transparent-proxy=yes

# Student Profile - Premium (Weekend/Holiday)
/ip hotspot user profile add name=siswa-premium \
    rate-limit=15M/30M \
    shared-users=1 \
    session-timeout=8h \
    idle-timeout=60m \
    keepalive-timeout=2m \
    status-autorefresh=1m \
    transparent-proxy=yes

:put "Step 2 Complete: User profiles created successfully"
:put "Profiles created: siswa-basic, siswa-extended, siswa-no-gaming, siswa-premium"
:put "Next: Import mikrotik_bandwidth_step3_mangle.rsc"
