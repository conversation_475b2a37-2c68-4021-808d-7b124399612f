# MikroTik Bandwidth Management - Step 3: Traffic Marking (Mangle Rules)
# Import this file after step2 to create traffic marking rules

# ============================================
# FIREWALL MANGLE RULES FOR GAMING TRAFFIC
# ============================================

# Mark gaming PC traffic connections - TCP
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Mark Gaming PC Traffic TCP" \
    dst-port-list=gaming-pc new-connection-mark=gaming-pc-conn \
    passthrough=yes protocol=tcp src-address=************/24

# Mark gaming PC traffic connections - UDP
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Mark Gaming PC Traffic UDP" \
    dst-port-list=gaming-pc new-connection-mark=gaming-pc-conn \
    passthrough=yes protocol=udp src-address=************/24

# Mark gaming mobile traffic connections - TCP
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Mark Gaming Mobile Traffic TCP" \
    dst-port-list=gaming-mobile new-connection-mark=gaming-mobile-conn \
    passthrough=yes protocol=tcp src-address=************/24

# Mark gaming mobile traffic connections - UDP
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Mark Gaming Mobile Traffic UDP" \
    dst-port-list=gaming-mobile new-connection-mark=gaming-mobile-conn \
    passthrough=yes protocol=udp src-address=************/24

# Mark gaming web traffic connections - TCP
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Mark Gaming Web Traffic TCP" \
    dst-port-list=gaming-web new-connection-mark=gaming-web-conn \
    passthrough=yes protocol=tcp src-address=************/24

# Mark gaming web traffic connections - UDP
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Mark Gaming Web Traffic UDP" \
    dst-port-list=gaming-web new-connection-mark=gaming-web-conn \
    passthrough=yes protocol=udp src-address=************/24

# Mark gaming packets for PC games
/ip firewall mangle add action=mark-packet chain=prerouting \
    comment="Mark Gaming PC Packets" \
    connection-mark=gaming-pc-conn new-packet-mark=gaming-pc-packet \
    passthrough=no

# Mark gaming packets for mobile games
/ip firewall mangle add action=mark-packet chain=prerouting \
    comment="Mark Gaming Mobile Packets" \
    connection-mark=gaming-mobile-conn new-packet-mark=gaming-mobile-packet \
    passthrough=no

# Mark gaming packets for web games
/ip firewall mangle add action=mark-packet chain=prerouting \
    comment="Mark Gaming Web Packets" \
    connection-mark=gaming-web-conn new-packet-mark=gaming-web-packet \
    passthrough=no

:put "Step 3 Complete: Traffic marking rules created successfully"
:put "Gaming traffic will now be identified and marked"
:put "Next: Import mikrotik_bandwidth_step4_queues.rsc"
