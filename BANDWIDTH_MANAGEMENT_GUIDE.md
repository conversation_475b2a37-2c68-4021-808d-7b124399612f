# MikroTik Bandwidth Management Implementation Guide

## Overview
This guide explains how to implement comprehensive bandwidth management for your school's MikroTik hotspot system, including gaming traffic control and student profile management.

## Current Configuration Analysis
Based on your existing `mikrotik_hotspot_config.rsc`, you have:
- Hotspot running on `ether1-LAN` (************/24)
- Basic student profile with 2M/2M limit
- Teacher profile with unlimited access
- Multiple bypassed devices for staff

## New Features Added

### 1. Gaming Traffic Management
- **Comprehensive Port Lists**: Covers 50+ popular games including:
  - PC Games: PUBG, DOTA 2, Counter-Strike, Fortnite, Steam games
  - Mobile Games: Free Fire, PUBG Mobile, Mobile Legends, Clash of Clans
  - Web Games: Roblox, 8 Ball Pool, League of Angels

- **Traffic Prioritization**: Gaming traffic gets priority queuing
- **Bandwidth Allocation**: 
  - Gaming Download: 5M guaranteed, up to 15M max
  - Gaming Upload: 2M guaranteed, up to 8M max

### 2. Enhanced Student Profiles

#### Three Student Profile Types:
1. **siswa-basic** (School Hours: 07:00-15:00)
   - Rate Limit: 1M upload / 2M download
   - Session Timeout: 4 hours
   - Idle Timeout: 30 minutes

2. **siswa-extended** (After School: 15:00-22:00)
   - Rate Limit: 2M upload / 4M download
   - Session Timeout: 6 hours
   - Idle Timeout: 45 minutes

3. **siswa-no-gaming** (Night Time: 22:00-07:00)
   - Rate Limit: 1M upload / 1M download
   - Session Timeout: 2 hours
   - Idle Timeout: 15 minutes

### 3. Time-Based Restrictions
- **School Hours (07:00-15:00)**: Restrictive bandwidth, social media blocked
- **After School (15:00-22:00)**: Increased bandwidth allowance
- **Night Time (22:00-07:00)**: Gaming restricted, minimal bandwidth

### 4. Content Filtering
- Social media blocking during school hours (Facebook, Instagram, Twitter, TikTok, YouTube)
- Gaming traffic monitoring and logging
- High bandwidth usage alerts

## Implementation Steps

### Step 1: Backup Current Configuration
```bash
# Export current configuration
/export file=backup-before-bandwidth-management
```

### Step 2: Apply Bandwidth Management Script
```bash
# Import the new bandwidth management configuration
/import file-name=mikrotik_bandwidth_management.rsc
```

### Step 3: Update Existing Users
```bash
# Update existing student users to use new profile
/ip hotspot user
set [find profile=siswa] profile=siswa-basic
```

### Step 4: Configure Bandwidth Limits (Adjust as needed)
```bash
# Modify total bandwidth limits based on your internet connection
/queue tree
set [find name=total-download] max-limit=YOUR_DOWNLOAD_SPEED
set [find name=total-upload] max-limit=YOUR_UPLOAD_SPEED
```

## Customization Options

### Bandwidth Limits Adjustment
You can modify the bandwidth limits in the queue tree section:

```bash
# Example: For 100M/50M internet connection
/queue tree
set [find name=total-download] max-limit=100M
set [find name=total-upload] max-limit=50M

# Adjust gaming priority allocation
set [find name=gaming-download] limit-at=10M max-limit=30M
set [find name=gaming-upload] limit-at=5M max-limit=15M
```

### Gaming Traffic Policy Options

#### Option 1: Prioritize Gaming (Current Default)
- Gaming traffic gets high priority
- Guaranteed bandwidth allocation
- Better gaming experience

#### Option 2: Restrict Gaming for Students
```bash
# Block gaming traffic for students during school hours
/ip firewall filter
add action=drop chain=forward comment="Block Gaming - School Hours" \
    connection-mark=gaming-pc-conn,gaming-mobile-conn,gaming-web-conn \
    src-address=************/24 time=7h-15h,mon,tue,wed,thu,fri
```

#### Option 3: Gaming Bandwidth Limitation
```bash
# Limit gaming traffic to specific bandwidth
/queue tree
set [find name=gaming-download] max-limit=5M
set [find name=gaming-upload] max-limit=2M
```

### Time Schedule Customization
```bash
# Modify school hours
/system scheduler
set [find name=school-hours-start] start-time=08:00:00
set [find name=after-school-start] start-time=16:00:00
set [find name=night-time-start] start-time=21:00:00
```

### Social Media Blocking Customization
```bash
# Add more sites to block
/ip firewall address-list
add address=snapchat.com list=social-media
add address=whatsapp.com list=social-media

# Modify blocking hours
/ip firewall filter
set [find comment="Block Social Media - School Hours"] time=8h-16h,mon,tue,wed,thu,fri
```

## Monitoring and Maintenance

### View Gaming Traffic Statistics
```bash
# Check gaming traffic logs
/log print where message~"GAMING-TRAFFIC"

# Monitor queue statistics
/queue tree print stats
```

### View Active Users and Bandwidth Usage
```bash
# Check active hotspot users
/ip hotspot active print

# Monitor bandwidth usage by user
/tool torch interface=ether1-LAN
```

### Troubleshooting Common Issues

1. **Gaming Still Slow**: Check if gaming ports are correctly identified
2. **Students Can't Access Internet**: Verify profile assignments
3. **Time-based Rules Not Working**: Check system clock and NTP sync
4. **High CPU Usage**: Consider reducing logging frequency

## Security Considerations

1. **Regular Updates**: Update gaming port lists as new games emerge
2. **Monitor Logs**: Check for unusual traffic patterns
3. **User Management**: Regularly review and clean up user accounts
4. **Backup Configuration**: Regular configuration backups

## Performance Optimization

1. **Queue Tree Optimization**: Adjust based on actual usage patterns
2. **Connection Limits**: Set appropriate connection limits per user
3. **Cache Configuration**: Consider web proxy for better performance
4. **Hardware Resources**: Monitor CPU and memory usage

## Support and Maintenance

- Review bandwidth usage weekly
- Update gaming port lists monthly
- Monitor system performance regularly
- Adjust time schedules based on school calendar changes
