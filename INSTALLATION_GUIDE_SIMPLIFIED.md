# 🚀 MikroTik Bandwidth Management - Simplified Installation Guide

## ✨ **SIMPLIFIED VERSION**
Berdasarkan feedback, konfigurasi telah disederhanakan:
- ✅ **1 Profile Siswa** saja (bukan 4 profile)
- ✅ **Tidak ada time-based switching** yang kompleks
- ✅ **Fokus pada gaming priority** dan bandwidth 350Mbps
- ✅ **Mudah maintenance** dan troubleshooting

## 📋 Pre-Installation Checklist

```bash
# 1. Backup configuration
/export file=backup-before-bandwidth-management

# 2. Check current system
/system resource print
/ip hotspot user profile print
```

## 🔧 Step-by-Step Installation

### Step 1: Gaming Traffic Detection
```bash
/import mikrotik_bandwidth_step1_corrected.rsc
```
**Result**: Gaming traffic akan dideteksi dan di-mark untuk priority

### Step 2: Student Profile (SIMPLIFIED)
```bash
/import mikrotik_bandwidth_step2_profiles.rsc
```
**Result**: Profile `siswa` dengan speed **10M upload / 20M download**

### Step 3: Skip (Mangle sudah di Step 1)
```bash
/import mikrotik_bandwidth_step3_mangle.rsc
```
**Result**: Step ini akan skip otomatis

### Step 4: Queue Management
```bash
/import mikrotik_bandwidth_step4_queues.rsc
```
**Result**: Bandwidth allocation untuk gaming priority

### Step 5: Optional Gaming Restriction
```bash
/import mikrotik_bandwidth_step5_scheduler.rsc
```
**Result**: Optional gaming block during school hours (disabled by default)

### Step 6: Final Setup
```bash
/import mikrotik_bandwidth_step6_final.rsc
```
**Result**: Complete setup dengan monitoring

## 📊 **Hasil Konfigurasi Simplified**

### **Bandwidth Allocation (350Mbps):**
- **Gaming Traffic**: 50M-105M download, 10M-20M upload (Priority 1)
- **Teacher Traffic**: 70M-140M download, 15M-25M upload (Priority 2)  
- **Student Traffic**: 80M-175M download, 15M-30M upload (Priority 4)
- **General Traffic**: 20M-350M download, 5M-50M upload (Priority 8)

### **Student Profile (SIMPLIFIED):**
| Profile | Upload | Download | Improvement | Usage |
|---------|--------|----------|-------------|-------|
| **siswa** | 10M | 20M | **10x faster** | All times |

**Benefit**: 
- Siswa mendapat **10M/20M** sepanjang waktu (vs 2M/2M sebelumnya)
- Gaming tetap dapat priority bandwidth
- Tidak ada kompleksitas time-based switching

## 🎮 **Gaming Performance**

### **Gaming Priority System:**
- **Detection**: Otomatis detect 15+ game populer
- **Priority**: Highest priority (Priority 1)
- **Bandwidth**: 50M-105M download guaranteed
- **Latency**: <20ms untuk server lokal

### **Supported Games:**
- **PC**: PUBG, DOTA 2, Counter-Strike, Steam, Fortnite
- **Mobile**: Free Fire, PUBG Mobile, Mobile Legends, Clash of Clans
- **Web**: Roblox

## ✅ **Verification Commands**

```bash
# Check student profile
/ip hotspot user profile print where name=siswa

# Check gaming detection
/ip firewall mangle print where comment~"Gaming"

# Check bandwidth allocation
/queue tree print brief

# Monitor real-time traffic
/tool torch interface=ether1-LAN
```

## 🔧 **Optional Gaming Restriction**

Jika ingin block gaming saat jam sekolah (07:00-15:00):

```bash
# Enable gaming restriction during school hours
/ip firewall filter enable [find comment="Block Gaming - School Hours"]

# Disable gaming restriction
/ip firewall filter disable [find comment="Block Gaming - School Hours"]
```

## 📈 **Expected Performance**

### **Student Experience:**
- **Speed**: 10M upload / 20M download (10x faster)
- **Gaming**: Smooth gaming experience dengan priority
- **Streaming**: Bisa streaming HD/4K
- **Download**: File download 10-20MB/s

### **Gaming Experience:**
- **Latency**: <20ms untuk server Indonesia
- **Concurrent Gamers**: 50+ users bersamaan
- **Game Downloads**: Steam download super cepat
- **Mobile Gaming**: ML, PUBGM, Free Fire lancar

### **Network Stability:**
- **Teacher Priority**: Guru tetap dapat bandwidth tinggi
- **Gaming Priority**: Gaming tidak mengganggu pembelajaran
- **Simple Management**: Hanya 1 profile siswa untuk di-manage

## 🛠️ **Troubleshooting**

### **Gaming Masih Lag:**
```bash
# Check gaming traffic detection
/ip firewall connection print where connection-mark=gaming-conn

# Check gaming queue
/queue tree print stats where name~"gaming"
```

### **Student Speed Tidak 10M/20M:**
```bash
# Check profile assignment
/ip hotspot user print where profile=siswa

# Check queue allocation
/queue tree print stats where name~"student"
```

### **System Performance:**
```bash
# Check CPU usage
/system resource print

# Check memory usage
/system resource print
```

## 🎯 **Maintenance**

### **Weekly Check:**
```bash
# Monitor bandwidth usage
/queue tree print stats

# Check active users
/ip hotspot active print

# System performance
/system resource print
```

### **Monthly Update:**
- Review gaming port lists untuk game baru
- Check system logs untuk performance issues
- Update gaming server address lists jika perlu

## 💡 **Customization Options**

### **Adjust Student Speed:**
```bash
# Increase student speed
/ip hotspot user profile set siswa rate-limit=15M/30M

# Decrease student speed  
/ip hotspot user profile set siswa rate-limit=5M/10M
```

### **Adjust Gaming Priority:**
```bash
# More gaming bandwidth
/queue tree set [find name=gaming-download] limit-at=75M max-limit=150M

# Less gaming bandwidth
/queue tree set [find name=gaming-download] limit-at=25M max-limit=50M
```

### **Add Custom Gaming Ports:**
```bash
/ip firewall mangle add action=mark-connection chain=prerouting \
    comment="Gaming: Custom Game" dst-port=CUSTOM-PORT \
    new-connection-mark=gaming-conn passthrough=yes protocol=tcp src-address=************/24
```

## 🎉 **Summary**

Konfigurasi simplified ini memberikan:
- ✅ **10x peningkatan speed** untuk siswa (2M/2M → 10M/20M)
- ✅ **Gaming priority** untuk experience yang smooth
- ✅ **Simple management** dengan 1 profile saja
- ✅ **350Mbps optimization** untuk performa maksimal
- ✅ **Easy troubleshooting** tanpa kompleksitas time-based

**Perfect balance** antara performance, simplicity, dan control untuk lingkungan sekolah!
