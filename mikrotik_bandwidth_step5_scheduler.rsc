# MikroTik Bandwidth Management - Step 5: Time-Based Scheduler
# Import this file after step4 to create automatic profile switching

# ============================================
# TIME-BASED SCHEDULER CONFIGURATION
# ============================================

# School hours scheduler (07:00-15:00) - Basic profile for students
/system scheduler add comment="Switch to Basic Student Profile - School Hours" \
    interval=1d name=school-hours-start \
    on-event=":foreach user in=[/ip hotspot user find profile~\"siswa-\"] do={/ip hotspot user set \$user profile=siswa-basic}" \
    start-date=2025-07-17 start-time=07:00:00

# After school hours (15:00-22:00) - Extended profile for students  
/system scheduler add comment="Switch to Extended Student Profile - After School" \
    interval=1d name=after-school-start \
    on-event=":foreach user in=[/ip hotspot user find profile~\"siswa-\"] do={/ip hotspot user set \$user profile=siswa-extended}" \
    start-date=2025-07-17 start-time=15:00:00

# Night time (22:00-07:00) - Gaming restricted profile
/system scheduler add comment="Switch to Gaming Restricted Profile - Night Time" \
    interval=1d name=night-time-start \
    on-event=":foreach user in=[/ip hotspot user find profile~\"siswa-\"] do={/ip hotspot user set \$user profile=siswa-no-gaming}" \
    start-date=2025-07-17 start-time=22:00:00

# Weekend scheduler - Premium profile (Saturday-Sunday)
/system scheduler add comment="Switch to Premium Student Profile - Weekend" \
    interval=1w name=weekend-start \
    on-event=":foreach user in=[/ip hotspot user find profile~\"siswa-\"] do={/ip hotspot user set \$user profile=siswa-premium}" \
    start-date=2025-07-19 start-time=08:00:00

# Monday scheduler - Back to basic profile
/system scheduler add comment="Switch Back to Basic Profile - Monday" \
    interval=1w name=monday-start \
    on-event=":foreach user in=[/ip hotspot user find profile~\"siswa-\"] do={/ip hotspot user set \$user profile=siswa-basic}" \
    start-date=2025-07-21 start-time=07:00:00

:put "Step 5 Complete: Time-based scheduler created successfully"
:put "Schedule:"
:put "- 07:00: Switch to siswa-basic (School hours)"
:put "- 15:00: Switch to siswa-extended (After school)"
:put "- 22:00: Switch to siswa-no-gaming (Night time)"
:put "- Weekend: Switch to siswa-premium"
:put "Next: Import mikrotik_bandwidth_step6_final.rsc"
