# MikroTik Bandwidth Management - Step 5: Time-Based Scheduler
# Import this file after step4 to create automatic profile switching

# ============================================
# TIME-BASED SCHEDULER - SIMPLIFIED (OPTIONAL)
# ============================================

# Since we now use single siswa profile, time-based scheduling is optional
# You can enable this if you want to restrict gaming during school hours

# Optional: Block gaming during school hours (07:00-15:00)
# Uncomment the lines below if you want to restrict gaming during school hours

# /ip firewall filter add action=drop chain=forward \
#     comment="Block Gaming - School Hours" \
#     connection-mark=gaming-conn \
#     src-address=************/24 time=7h-15h,mon,tue,wed,thu,fri disabled=yes

:put "Step 5 Complete: Time-based scheduler simplified"
:put "Single siswa profile (10M/20M) is now used for all times"
:put "Optional gaming restriction during school hours is available but disabled"
:put "To enable gaming restriction during school hours:"
:put "/ip firewall filter enable [find comment=\"Block Gaming - School Hours\"]"
:put "Next: Import mikrotik_bandwidth_step6_final.rsc"
