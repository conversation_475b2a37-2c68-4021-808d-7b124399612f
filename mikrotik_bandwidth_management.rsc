# MikroTik Bandwidth Management Script with Gaming Traffic Control
# Compatible with existing hotspot configuration
# Created: 2025-07-17
# 
# This script provides:
# 1. Gaming traffic identification and management
# 2. Enhanced student profile bandwidth control
# 3. Time-based restrictions
# 4. Queue management for different traffic types

# ============================================
# GAMING PORT LISTS CONFIGURATION
# ============================================

# Create address lists for gaming traffic identification
/ip firewall address-list
add address=0.0.0.0/0 comment="Gaming Traffic - All destinations" list=gaming-destinations

# Create port groups for different game categories
/ip firewall port-list
# PC Games - Split into multiple entries to avoid syntax errors
add comment="APEX Legends TCP" list=gaming-pc ports=9960-9969,1024-1124,3216,18000,18120,18060
add comment="APEX Legends TCP2" list=gaming-pc ports=27900,28910,29900
add comment="PUBG PC" list=gaming-pc ports=7080-8000
add comment="Counter-Strike GO TCP" list=gaming-pc ports=27015-27030,27036-27037
add comment="Counter-Strike GO UDP" list=gaming-pc ports=4380,27000-27031
add comment="DOTA 2 TCP" list=gaming-pc ports=9100-9200,8230-8250,8110-8120
add comment="DOTA 2 UDP" list=gaming-pc ports=28010-28200,27010-27200,39000
add comment="Fortnite" list=gaming-pc ports=9000-9100
add comment="Steam Client" list=gaming-pc ports=27000-27015,27015-27030
add comment="Steam Downloads" list=gaming-pc ports=27014-27050
add comment="Steam Voice" list=gaming-pc ports=3478,4379,4380
add comment="Point Blank TCP" list=gaming-pc ports=39190-39200
add comment="Point Blank UDP" list=gaming-pc ports=40000-40010
add comment="Cross Fire TCP" list=gaming-pc ports=10009,13008,16666,28012
add comment="Cross Fire UDP" list=gaming-pc ports=12020-12080,13000-13080
add comment="Dragon Nest TCP" list=gaming-pc ports=14300-15512
add comment="Dragon Nest UDP" list=gaming-pc ports=15000-15500

# Mobile Games - Split for better compatibility
add comment="Free Fire Mobile TCP" list=gaming-mobile ports=39003,39698,39779
add comment="Free Fire Mobile UDP" list=gaming-mobile ports=10001,10003,10012
add comment="PUBG Mobile TCP" list=gaming-mobile ports=10012,17500
add comment="PUBG Mobile UDP1" list=gaming-mobile ports=10010,10013,10039,10096,10491
add comment="PUBG Mobile UDP2" list=gaming-mobile ports=10612,11455,12235,13748,13894
add comment="PUBG Mobile UDP3" list=gaming-mobile ports=13972,20000-20002
add comment="Mobile Legends TCP" list=gaming-mobile ports=5001,5003,9001
add comment="Mobile Legends Range" list=gaming-mobile ports=30000-30200
add comment="Clash of Clans" list=gaming-mobile ports=9330-9340
add comment="Arena of Valor TCP" list=gaming-mobile ports=10001-10094
add comment="Arena of Valor UDP" list=gaming-mobile ports=10080,17000
add comment="Rules of Survival Mobile" list=gaming-mobile ports=24000-24050

# Web Games
add comment="Roblox" list=gaming-web ports=49152-65535
add comment="8 Ball Pool" list=gaming-web ports=4000
add comment="League of Angels 2" list=gaming-web ports=51700-51715

# ============================================
# ENHANCED USER PROFILES
# ============================================

# Remove existing siswa profile and create enhanced versions
/ip hotspot user profile
remove [find name=siswa]

# Student Profile - Basic (School Hours: 07:00-15:00) - Optimized for 350Mbps
add name=siswa-basic \
    rate-limit=5M/10M \
    shared-users=1 \
    session-timeout=4h \
    idle-timeout=30m \
    keepalive-timeout=2m \
    status-autorefresh=1m \
    transparent-proxy=yes

# Student Profile - Extended (After School: 15:00-22:00) - Higher speeds
add name=siswa-extended \
    rate-limit=10M/20M \
    shared-users=1 \
    session-timeout=6h \
    idle-timeout=45m \
    keepalive-timeout=2m \
    status-autorefresh=1m \
    transparent-proxy=yes

# Student Profile - Gaming Restricted (Night time)
add name=siswa-no-gaming \
    rate-limit=3M/5M \
    shared-users=1 \
    session-timeout=2h \
    idle-timeout=15m \
    keepalive-timeout=2m \
    status-autorefresh=1m \
    transparent-proxy=yes

# Student Profile - Premium (Weekend/Holiday)
add name=siswa-premium \
    rate-limit=15M/30M \
    shared-users=1 \
    session-timeout=8h \
    idle-timeout=60m \
    keepalive-timeout=2m \
    status-autorefresh=1m \
    transparent-proxy=yes

# ============================================
# FIREWALL RULES FOR GAMING TRAFFIC
# ============================================

# Mark gaming traffic connections
/ip firewall mangle
add action=mark-connection chain=prerouting comment="Mark Gaming PC Traffic" \
    dst-port-list=gaming-pc new-connection-mark=gaming-pc-conn passthrough=yes \
    protocol=tcp src-address=************/24

add action=mark-connection chain=prerouting comment="Mark Gaming PC Traffic UDP" \
    dst-port-list=gaming-pc new-connection-mark=gaming-pc-conn passthrough=yes \
    protocol=udp src-address=************/24

add action=mark-connection chain=prerouting comment="Mark Gaming Mobile Traffic" \
    dst-port-list=gaming-mobile new-connection-mark=gaming-mobile-conn passthrough=yes \
    protocol=tcp src-address=************/24

add action=mark-connection chain=prerouting comment="Mark Gaming Mobile Traffic UDP" \
    dst-port-list=gaming-mobile new-connection-mark=gaming-mobile-conn passthrough=yes \
    protocol=udp src-address=************/24

add action=mark-connection chain=prerouting comment="Mark Gaming Web Traffic" \
    dst-port-list=gaming-web new-connection-mark=gaming-web-conn passthrough=yes \
    protocol=tcp src-address=************/24

# Mark gaming packets
add action=mark-packet chain=prerouting comment="Mark Gaming PC Packets" \
    connection-mark=gaming-pc-conn new-packet-mark=gaming-pc-packet passthrough=no

add action=mark-packet chain=prerouting comment="Mark Gaming Mobile Packets" \
    connection-mark=gaming-mobile-conn new-packet-mark=gaming-mobile-packet passthrough=no

add action=mark-packet chain=prerouting comment="Mark Gaming Web Packets" \
    connection-mark=gaming-web-conn new-packet-mark=gaming-web-packet passthrough=no

# ============================================
# QUEUE TREE CONFIGURATION
# ============================================

# Main bandwidth allocation (350Mbps connection optimized)
/queue tree
add comment="Total Download Bandwidth - 350Mbps" max-limit=350M name=total-download parent=ether1-LAN
add comment="Total Upload Bandwidth - 50Mbps" max-limit=50M name=total-upload parent=ether2-WAN

# Gaming traffic queues (High Priority - 30% allocation)
add comment="Gaming Download Priority" limit-at=50M max-limit=105M name=gaming-download \
    packet-mark=gaming-pc-packet,gaming-mobile-packet,gaming-web-packet parent=total-download priority=1

add comment="Gaming Upload Priority" limit-at=10M max-limit=20M name=gaming-upload \
    packet-mark=gaming-pc-packet,gaming-mobile-packet,gaming-web-packet parent=total-upload priority=1

# Teacher traffic queues (High priority - 40% allocation)
add comment="Teacher Download" limit-at=70M max-limit=140M name=teacher-download parent=total-download priority=2
add comment="Teacher Upload" limit-at=15M max-limit=25M name=teacher-upload parent=total-upload priority=2

# Student traffic queues (Lower priority - 50% allocation)
add comment="Student Download" limit-at=80M max-limit=175M name=student-download parent=total-download priority=4
add comment="Student Upload" limit-at=15M max-limit=30M name=student-upload parent=total-upload priority=4

# General traffic queue (Lowest priority - remaining bandwidth)
add comment="General Download" limit-at=20M max-limit=350M name=general-download parent=total-download priority=8
add comment="General Upload" limit-at=5M max-limit=50M name=general-upload parent=total-upload priority=8

# ============================================
# TIME-BASED SCHEDULER
# ============================================

# School hours scheduler (07:00-15:00) - Basic profile for students
/system scheduler
add comment="Switch to Basic Student Profile - School Hours" interval=1d name=school-hours-start \
    on-event=":foreach user in=[/ip hotspot user find profile=siswa-extended or profile=siswa-no-gaming or profile=siswa-premium] do={/ip hotspot user set \$user profile=siswa-basic}" \
    start-date=2025-07-17 start-time=07:00:00

# After school hours (15:00-22:00) - Extended profile for students
add comment="Switch to Extended Student Profile - After School" interval=1d name=after-school-start \
    on-event=":foreach user in=[/ip hotspot user find profile=siswa-basic or profile=siswa-no-gaming] do={/ip hotspot user set \$user profile=siswa-extended}" \
    start-date=2025-07-17 start-time=15:00:00

# Night time (22:00-07:00) - Gaming restricted profile
add comment="Switch to Gaming Restricted Profile - Night Time" interval=1d name=night-time-start \
    on-event=":foreach user in=[/ip hotspot user find profile=siswa-basic or profile=siswa-extended] do={/ip hotspot user set \$user profile=siswa-no-gaming}" \
    start-date=2025-07-17 start-time=22:00:00

# Weekend scheduler - Premium profile (Saturday-Sunday)
add comment="Switch to Premium Student Profile - Weekend" interval=1w name=weekend-start \
    on-event=":foreach user in=[/ip hotspot user find profile~\"siswa-\"] do={/ip hotspot user set \$user profile=siswa-premium}" \
    start-date=2025-07-19 start-time=08:00:00

# Monday scheduler - Back to basic profile
add comment="Switch Back to Basic Profile - Monday" interval=1w name=monday-start \
    on-event=":foreach user in=[/ip hotspot user find profile=siswa-premium] do={/ip hotspot user set \$user profile=siswa-basic}" \
    start-date=2025-07-21 start-time=07:00:00

# ============================================
# CONTENT FILTERING FOR STUDENTS
# ============================================

# Block social media during school hours (optional)
/ip firewall filter
add action=reject chain=forward comment="Block Social Media - School Hours" \
    dst-address-list=social-media reject-with=icmp-network-unreachable \
    src-address=************/24 time=7h-15h,mon,tue,wed,thu,fri

# Create social media address list
/ip firewall address-list
add address=facebook.com comment="Social Media Block" list=social-media
add address=instagram.com comment="Social Media Block" list=social-media
add address=twitter.com comment="Social Media Block" list=social-media
add address=tiktok.com comment="Social Media Block" list=social-media
add address=youtube.com comment="Social Media Block" list=social-media

# ============================================
# MONITORING AND LOGGING
# ============================================

# Log gaming traffic
/ip firewall filter
add action=passthrough chain=forward comment="Log Gaming Traffic" \
    connection-mark=gaming-pc-conn,gaming-mobile-conn,gaming-web-conn log=yes \
    log-prefix="GAMING-TRAFFIC"

# Log high bandwidth usage
add action=passthrough chain=forward comment="Log High Bandwidth Usage" \
    connection-bytes=100000000-4294967295 log=yes log-prefix="HIGH-BANDWIDTH"
