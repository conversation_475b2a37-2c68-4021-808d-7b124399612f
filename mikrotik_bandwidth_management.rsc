# MikroTik Bandwidth Management Script with Gaming Traffic Control
# Compatible with existing hotspot configuration
# Created: 2025-07-17
# 
# This script provides:
# 1. Gaming traffic identification and management
# 2. Enhanced student profile bandwidth control
# 3. Time-based restrictions
# 4. Queue management for different traffic types

# ============================================
# GAMING PORT LISTS CONFIGURATION
# ============================================

# Create address lists for gaming traffic identification
/ip firewall address-list
add address=0.0.0.0/0 comment="Gaming Traffic - All destinations" list=gaming-destinations

# Create port groups for different game categories
/ip firewall port-list
# PC Games
add comment="APEX Legends" list=gaming-pc ports=9960-9969,1024-1124,3216,18000,18120,18060,27900,28910,29900
add comment="PUBG PC" list=gaming-pc ports=7080-8000
add comment="Counter-Strike GO" list=gaming-pc ports=27015-27030,27036-27037,4380,27000-27031,27036
add comment="DOTA 2" list=gaming-pc ports=9100-9200,8230-8250,8110-8120,28010-28200,27010-27200,39000
add comment="Fortnite" list=gaming-pc ports=9000-9100
add comment="Steam General" list=gaming-pc ports=27000-27015,27015-27030,27014-27050,4380,3478,4379,4380
add comment="Point Blank" list=gaming-pc ports=39190-39200,40000-40010
add comment="Cross Fire" list=gaming-pc ports=10009,13008,16666,28012,12020-12080,13000-13080
add comment="Dragon Nest" list=gaming-pc ports=14300-15512,15000-15500

# Mobile Games
add comment="Free Fire Mobile" list=gaming-mobile ports=39003,39698,39779,10001,10003,10012
add comment="PUBG Mobile" list=gaming-mobile ports=10012,17500,10010,10013,10039,10096,10491,10612,11455,12235,13748,13894,13972,20000-20002
add comment="Mobile Legends" list=gaming-mobile ports=5001,5003,9001,30000-30200
add comment="Clash of Clans" list=gaming-mobile ports=9330-9340
add comment="Arena of Valor" list=gaming-mobile ports=10001-10094,10080,17000
add comment="Rules of Survival Mobile" list=gaming-mobile ports=24000-24050

# Web Games
add comment="Roblox" list=gaming-web ports=49152-65535
add comment="8 Ball Pool" list=gaming-web ports=4000
add comment="League of Angels 2" list=gaming-web ports=51700-51715

# ============================================
# ENHANCED USER PROFILES
# ============================================

# Remove existing siswa profile and create enhanced versions
/ip hotspot user profile
remove [find name=siswa]

# Student Profile - Basic (School Hours: 07:00-15:00)
add name=siswa-basic \
    rate-limit=1M/2M \
    shared-users=1 \
    session-timeout=4h \
    idle-timeout=30m \
    keepalive-timeout=2m \
    status-autorefresh=1m \
    transparent-proxy=yes

# Student Profile - Extended (After School: 15:00-22:00)
add name=siswa-extended \
    rate-limit=2M/4M \
    shared-users=1 \
    session-timeout=6h \
    idle-timeout=45m \
    keepalive-timeout=2m \
    status-autorefresh=1m \
    transparent-proxy=yes

# Student Profile - Gaming Restricted
add name=siswa-no-gaming \
    rate-limit=1M/1M \
    shared-users=1 \
    session-timeout=2h \
    idle-timeout=15m \
    keepalive-timeout=2m \
    status-autorefresh=1m \
    transparent-proxy=yes

# ============================================
# FIREWALL RULES FOR GAMING TRAFFIC
# ============================================

# Mark gaming traffic connections
/ip firewall mangle
add action=mark-connection chain=prerouting comment="Mark Gaming PC Traffic" \
    dst-port-list=gaming-pc new-connection-mark=gaming-pc-conn passthrough=yes \
    protocol=tcp src-address=************/24

add action=mark-connection chain=prerouting comment="Mark Gaming PC Traffic UDP" \
    dst-port-list=gaming-pc new-connection-mark=gaming-pc-conn passthrough=yes \
    protocol=udp src-address=************/24

add action=mark-connection chain=prerouting comment="Mark Gaming Mobile Traffic" \
    dst-port-list=gaming-mobile new-connection-mark=gaming-mobile-conn passthrough=yes \
    protocol=tcp src-address=************/24

add action=mark-connection chain=prerouting comment="Mark Gaming Mobile Traffic UDP" \
    dst-port-list=gaming-mobile new-connection-mark=gaming-mobile-conn passthrough=yes \
    protocol=udp src-address=************/24

add action=mark-connection chain=prerouting comment="Mark Gaming Web Traffic" \
    dst-port-list=gaming-web new-connection-mark=gaming-web-conn passthrough=yes \
    protocol=tcp src-address=************/24

# Mark gaming packets
add action=mark-packet chain=prerouting comment="Mark Gaming PC Packets" \
    connection-mark=gaming-pc-conn new-packet-mark=gaming-pc-packet passthrough=no

add action=mark-packet chain=prerouting comment="Mark Gaming Mobile Packets" \
    connection-mark=gaming-mobile-conn new-packet-mark=gaming-mobile-packet passthrough=no

add action=mark-packet chain=prerouting comment="Mark Gaming Web Packets" \
    connection-mark=gaming-web-conn new-packet-mark=gaming-web-packet passthrough=no

# ============================================
# QUEUE TREE CONFIGURATION
# ============================================

# Main bandwidth allocation
/queue tree
add comment="Total Download Bandwidth" max-limit=50M name=total-download parent=ether1-LAN
add comment="Total Upload Bandwidth" max-limit=20M name=total-upload parent=ether2-WAN

# Gaming traffic queues (Priority traffic)
add comment="Gaming Download Priority" limit-at=5M max-limit=15M name=gaming-download \
    packet-mark=gaming-pc-packet,gaming-mobile-packet,gaming-web-packet parent=total-download priority=1

add comment="Gaming Upload Priority" limit-at=2M max-limit=8M name=gaming-upload \
    packet-mark=gaming-pc-packet,gaming-mobile-packet,gaming-web-packet parent=total-upload priority=1

# Student traffic queues (Lower priority)
add comment="Student Download" limit-at=10M max-limit=25M name=student-download parent=total-download priority=4
add comment="Student Upload" limit-at=5M max-limit=10M name=student-upload parent=total-upload priority=4

# Teacher traffic queues (High priority)
add comment="Teacher Download" limit-at=15M max-limit=35M name=teacher-download parent=total-download priority=2
add comment="Teacher Upload" limit-at=8M max-limit=15M name=teacher-upload parent=total-upload priority=2

# ============================================
# TIME-BASED SCHEDULER
# ============================================

# School hours scheduler (07:00-15:00) - Restrictive for students
/system scheduler
add comment="Switch to Basic Student Profile - School Hours" interval=1d name=school-hours-start \
    on-event="/ip hotspot user profile set [find name=siswa-basic] rate-limit=1M/2M" \
    start-date=2025-07-17 start-time=07:00:00

# After school hours (15:00-22:00) - More bandwidth for students
add comment="Switch to Extended Student Profile - After School" interval=1d name=after-school-start \
    on-event="/ip hotspot user profile set [find name=siswa-extended] rate-limit=2M/4M" \
    start-date=2025-07-17 start-time=15:00:00

# Night time (22:00-07:00) - Gaming restricted
add comment="Switch to Gaming Restricted Profile - Night Time" interval=1d name=night-time-start \
    on-event="/ip hotspot user profile set [find name=siswa-no-gaming] rate-limit=1M/1M" \
    start-date=2025-07-17 start-time=22:00:00

# ============================================
# CONTENT FILTERING FOR STUDENTS
# ============================================

# Block social media during school hours (optional)
/ip firewall filter
add action=reject chain=forward comment="Block Social Media - School Hours" \
    dst-address-list=social-media reject-with=icmp-network-unreachable \
    src-address=************/24 time=7h-15h,mon,tue,wed,thu,fri

# Create social media address list
/ip firewall address-list
add address=facebook.com comment="Social Media Block" list=social-media
add address=instagram.com comment="Social Media Block" list=social-media
add address=twitter.com comment="Social Media Block" list=social-media
add address=tiktok.com comment="Social Media Block" list=social-media
add address=youtube.com comment="Social Media Block" list=social-media

# ============================================
# MONITORING AND LOGGING
# ============================================

# Log gaming traffic
/ip firewall filter
add action=passthrough chain=forward comment="Log Gaming Traffic" \
    connection-mark=gaming-pc-conn,gaming-mobile-conn,gaming-web-conn log=yes \
    log-prefix="GAMING-TRAFFIC"

# Log high bandwidth usage
add action=passthrough chain=forward comment="Log High Bandwidth Usage" \
    connection-bytes=100000000-4294967295 log=yes log-prefix="HIGH-BANDWIDTH"
